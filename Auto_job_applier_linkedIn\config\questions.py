'''
Author:     <PERSON>
LinkedIn:   https://www.linkedin.com/in/saivigneshgolla/

Copyright (C) 2024 Sai Vignesh Golla

License:    GNU Affero General Public License
            https://www.gnu.org/licenses/agpl-3.0.en.html
            
GitHub:     https://github.com/GodsScion/Auto_job_applier_linkedIn

version:    24.12.29.12.30
'''


###################################################### APPLICATION INPUTS ######################################################


# >>>>>>>>>>> Easy Apply Questions & Inputs <<<<<<<<<<<

# Give an relative path of your default resume to be uploaded. If file in not found, will continue using your previously uploaded resume in LinkedIn.
default_resume_path = "all resumes/default/schin_m resume.pdf"  # (In Development)

# What do you want to answer for questions that ask about years of experience you have, this is different from current_experience?
years_of_experience = "3"          # A number in quotes Eg: "0","1","2","3","4", etc.

# Do you need visa sponsorship now or in future?
require_visa = "No"               # "Yes" or "No"

# What is the link to your portfolio website, leave it empty as "", if you want to leave this question unanswered
website = "https://github.com/GodsScion"                        # "www.example.bio" or "" and so on....

# Please provide the link to your LinkedIn profile.
linkedIn = "https://www.linkedin.com/in/sachin-m99/"       # "https://www.linkedin.com/in/example" or "" and so on...

# What is the status of your citizenship? # If left empty as "", tool will not answer the question. However, note that some companies make it compulsory to be answered
# Valid options are: "U.S. Citizen/Permanent Resident", "Non-citizen allowed to work for any employer", "Non-citizen allowed to work for current employer", "Non-citizen seeking work authorization", "Canadian Citizen/Permanent Resident" or "Other"
us_citizenship = "U.S. Citizen/Permanent Resident"



## SOME ANNOYING QUESTIONS BY COMPANIES 🫠 ##

# What to enter in your desired salary question (American and European), What is your expected CTC (South Asian and others)?, only enter in numbers as some companies only allow numbers,
desired_salary = 1000000          # 80000, 90000, 100000 or 120000 and so on... Do NOT use quotes
'''
Note: If question has the word "lakhs" in it (Example: What is your expected CTC in lakhs), 
then it will add '.' before last 5 digits and answer. Examples: 
* 2400000 will be answered as "24.00"
* 850000 will be answered as "8.50"
And if asked in months, then it will divide by 12 and answer. Examples:
* 2400000 will be answered as "200000"
* 850000 will be answered as "70833"
'''

# What is your current CTC? Some companies make it compulsory to be answered in numbers...
current_ctc = 800000            # 800000, 900000, 1000000 or 1200000 and so on... Do NOT use quotes
'''
Note: If question has the word "lakhs" in it (Example: What is your current CTC in lakhs), 
then it will add '.' before last 5 digits and answer. Examples: 
* 2400000 will be answered as "24.00"
* 850000 will be answered as "8.50"
# And if asked in months, then it will divide by 12 and answer. Examples:
# * 2400000 will be answered as "200000"
# * 850000 will be answered as "70833"
'''

# (In Development) # Currency of salaries you mentioned. Companies that allow string inputs will add this tag to the end of numbers. Eg: 
# currency = "INR"                 # "USD", "INR", "EUR", etc.

# What is your notice period in days?
notice_period = 0                   # Any number >= 0 without quotes. Eg: 0, 7, 15, 30, 45, etc.
'''
Note: If question has 'month' or 'week' in it (Example: What is your notice period in months), 
then it will divide by 30 or 7 and answer respectively. Examples:
* For notice_period = 66:
  - "66" OR "2" if asked in months OR "9" if asked in weeks
* For notice_period = 15:"
  - "15" OR "0" if asked in months OR "2" if asked in weeks
* For notice_period = 0:
  - "0" OR "0" if asked in months OR "0" if asked in weeks
'''

# Your LinkedIn headline in quotes Eg: "Software Engineer @ Google, Masters in Computer Science", "Recent Grad Student @ MIT, Computer Science"
linkedin_headline = "Software Engineer |SDE 1 |  React.js, Node.js, React Native | Full Stack Developer | 2+ years experience | Open for SDE roles" # "Headline" or "" to leave this question unanswered

# Your summary in quotes, use \n to add line breaks if using single quotes "Summary".You can skip \n if using triple quotes """Summary"""
linkedin_summary = """
I'm a Software Engineer with 2+ years of experience in full-stack web and mobile development.
Specialized in React.js, React Native, Node.js, Express.js, TypeScript, and cloud platforms (AWS, Cloudflare).
Built production-ready applications including MyTeal (mental health platform) and TradeStreak (crypto app) with proven performance improvements.
Experienced in serverless architecture, API development, mobile app development, and modern web technologies.
Winner of Agoric and Rise In Fast Hack Hackathon (Web3) and finalist at Bank of Baroda Hackathon.
"""

'''
Note: If left empty as "", the tool will not answer the question. However, note that some companies make it compulsory to be answered. Use \n to add line breaks.
''' 

# Your cover letter in quotes, use \n to add line breaks if using single quotes "Cover Letter".You can skip \n if using triple quotes """Cover Letter""" (This question makes sense though)
cover_letter = """
Cover Letter
"""
##> ------ Dheeraj Deshwal : dheeraj9811 Email:<EMAIL>/<EMAIL> - Feature ------

# Your user_information_all letter in quotes, use \n to add line breaks if using single quotes "user_information_all".You can skip \n if using triple quotes """user_information_all""" (This question makes sense though)
# We use this to pass to AI to generate answer from information , Assuing Information contians eg: resume  all the information like name, experience, skills, Country, any illness etc.
user_information_all ="""
Name: Sachin M
Email: <EMAIL>
Phone: +91 **********
Location: Bangalore, Karnataka, India
GitHub: https://github.com/sachin-m99
LinkedIn: https://www.linkedin.com/in/sachin-m99/

PROFESSIONAL EXPERIENCE:
Software Engineer Intern @ Synapsis Medical INC (Edmonton, Canada) - Apr 2025
- Architected MyTeal, a production-ready React Native mental health platform with AI-powered ChatGPT integration
- Built real-time symptom tracking with SQLite database, interactive video learning system, and Firebase authentication
- Implemented 15+ interconnected features with 99.5% crash-free performance through comprehensive error handling
- Engineered high-performance state management using Zustand with MMKV persistence (10x faster than AsyncStorage)
- Built sophisticated mobile UI/UX with React Native Reanimated 3.17.5 worklets for 60fps animations
- Designed 35+ RESTful endpoints with Meta Graph API and Shopify API integrations
- Reduced manual data collection by 70% and improved backend response times by 25%

Software Engineer Intern @ Qixbi (Bangalore, Remote) - Oct 2024 – Jan 2025
- Built and launched TradeStreak, a crypto investment and analysis app using React Native, Tailwind CSS, and Expo
- Drove 30% increase in downloads within first month on Google Play Store
- Optimized RESTful API integration, reducing data latency by 30%
- Implemented Sentry for real-time monitoring, leading to 20% reduction in post-launch error rates

TECHNICAL SKILLS:
- Technologies: Node.js, React.js, Next.js, Express.js, Tailwind CSS, Prisma, TurboRepo, PostgreSQL, HTML5, CSS3, MongoDB, AWS S3/EC2, Cloudflare, Git, Docker, Hono, Azure, React Native, Redux, Jest, REST APIs
- Programming Languages: C/C++, JavaScript (ES6+), TypeScript
- Concepts: Data Structures and Algorithms, Bit Manipulation, Computer Networks, Operating Systems, Object-Oriented Programming, Database Management Systems (DBMS), Linux

SELECTED PROJECTS:
Exponus - High-performance, globally distributed serverless API using Hono on Cloudflare Workers, Prisma ORM with PostgreSQL, achieving sub-200ms response times for Medium-style blogging platform

EDUCATION:
East West College of Engineering and Management (2022-2025) - Bangalore, India
Bachelor of Computer Applications – 8.2 CGPA

ACHIEVEMENTS:
- Agoric and Rise In Fast Hack Hackathon (Web3): Winner
- Bank of Baroda Hackathon: Finalist
- Solved 280+ DSA problems on LeetCode, GeeksforGeeks

PERSONAL INFO:
- Experience: 3+ years in full-stack development
- Expected Salary: 10-12 LPA
- Current CTC: 8 LPA
- Notice Period: 0 days (immediate joiner)
- Visa Status: Indian citizen, no visa sponsorship required
- Availability: Full-time, Internship positions
- Work Preference: On-site, Hybrid, Remote
- Languages: English (fluent), Hindi (native)
"""
##<
'''
Note: If left empty as "", the tool will not answer the question. However, note that some companies make it compulsory to be answered. Use \n to add line breaks.
''' 

# Name of your most recent employer
recent_employer = "Not Applicable" # "", "Lala Company", "Google", "Snowflake", "Databricks"

# Example question: "On a scale of 1-10 how much experience do you have building web or mobile applications? 1 being very little or only in school, 10 being that you have built and launched applications to real users"
confidence_level = "8"             # Any number between "1" to "10" including 1 and 10, put it in quotes ""
##



# >>>>>>>>>>> RELATED SETTINGS <<<<<<<<<<<

## Allow Manual Inputs
# Should the tool pause before every submit application during easy apply to let you check the information?
pause_before_submit = False         # True or False, Note: True or False are case-sensitive
'''
Note: Will be treated as False if `run_in_background = True`
'''

# Should the tool pause if it needs help in answering questions during easy apply?
# Note: If set as False will answer randomly...
pause_at_failed_question = False   # True or False, Note: True or False are case-sensitive
'''
Note: Will be treated as False if `run_in_background = True`
'''
##

# Do you want to overwrite previous answers?
overwrite_previous_answers = False # True or False, Note: True or False are case-sensitive







############################################################################################################
'''
THANK YOU for using my tool 😊! Wishing you the best in your job hunt 🙌🏻!

Sharing is caring! If you found this tool helpful, please share it with your peers 🥺. Your support keeps this project alive.

Support my work on <PATREON_LINK>. Together, we can help more job seekers.

As an independent developer, I pour my heart and soul into creating tools like this, driven by the genuine desire to make a positive impact.

Your support, whether through donations big or small or simply spreading the word, means the world to me and helps keep this project alive and thriving.

Gratefully yours 🙏🏻,
Sai Vignesh Golla
'''
############################################################################################################