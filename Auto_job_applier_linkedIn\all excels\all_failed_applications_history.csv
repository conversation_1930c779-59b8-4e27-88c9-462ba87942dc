Job ID,Job Link,Resume Tried,Date listed,Date Tried,Assumed Reason,Stack Trace,External Job link,Screenshot Name
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:34:56.347416,2025-07-25 17:34:56.458735,Found a Bad Word in About Job,"
About the job
Type: Contract (C2H)
Duration: 6 Months 
Location: Bangalore (Hybrid 3 Days )
Rate: 18 LPA
Experience: 6 to 9 Years

Looking for an experienced Hyperion support Developer with 5-8 years of relevant experience. Candidates with financial background is preferred. The detailed responsibilities are mentioned below.
HFM, Planning, Essbase, HPCM
Perform coding and configuration to enhance and maintain Oracle EPM tools or Hyperion applications, including Planning, Essbase (BSO and ASO cubes), FDMEE,HPCM.
Monitor, maintain security, management process controls, task flows, business rules, scripts, member lists, journal module, objects (Webforms, Grids, Task lists), consolidation and data clearing procedures, metadata updates etc.
Strong knowledge of Essbase scripting and Calc Manager Rules, Essbase Calc Scripts, Batch scripts, MDX & MAXL. EPM solution, Essbase Cubes.
Work closely with Master data team and finance teams to manage metadata changes, business rule updates, form/dashboard enhancements, and data loads.
Have good general functional knowledge and understanding of budgeting, forecasting and financial analysis and close processes
Sound knowledge of Life Cycle Management, user Management, Security Management etc
Provide level one and level two support in line with the team’s remit: CoA validations and deployments using EPMA / DRM; User support and setups.
Good analytical, problem solving, & communication skills
Have experience working with ServiceNow ticketing tool, approval process and SLA.
Address user queries with webforms, SmartView, data reconciliation in HFM
 Read, understand and update HFM rules per user requirements
 Update HFM application metadata and security as required
 Re-shell application and reconcile data for regression testing before monthly updates.
 Deploy updates to Pre-Prod and reconcile data before monthly updates in HFM and Planning/Essbase apps.
 Deploy changes to Production after preproduction COA and new enhancements testing.
 FDMEE: Monitor data loading schedule jobs and if any failures/kickouts. Address these by liaising with entity owners (in the case of data quality errors) or updating dimension maps

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4275601334,https://www.linkedin.com/jobs/view/4275601334,Pending,2025-07-25 16:36:20.520550,2025-07-25 17:36:20.678226,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275596055,https://www.linkedin.com/jobs/view/4275596055,Pending,2025-07-25 16:36:26.786024,2025-07-25 17:36:26.930898,Required experience is high,"
About the job
We have a Walk-In Drive for Java Full Stack Developer requirement on 28th&29th July 25.

Drive venue:
Address: Adobe Tower, Block A, Prestige Platina Tech Park, Marathahalli-Sarjapur Outer Ring Road, Kadubeesanahalli



Java Full Stack Developer: 

Core Java with Microservices - 40%
Cloud(Azure/AWS) - 20%
Rest API integration - 10%
Angular/ReactJS - 10%
SQL - 10%
SpringBoot - 10%


Location : Bengaluru - Hybrid
NP : 30days
Experience :6 to 10 yrs
Budget : Max 30 LPA
Payroll : STL - Sterlite Technologies Limited

JD :
Job Title : Java Full Stack Developer
Work Location : Bengaluru
Experience : 6-8 Yrs.
Job Mode : Hybrid
Responsibilities:
Candidate must have hands-on implementation experience with Java.
Candidate will need to work on Design and Development of business-critical solutions using cloud-based services on AWS/Azure.
Candidate will need to work closely with other teams in IT including Business Solution Analyst, QA, Release Management, and Operations.
Candidate will be responsible for collaborating with other engineers and project stakeholders on the design and development of solutions and integrations.
Candidate will be responsible for building and unit testing the integrations using the appropriate technology. They will work closely with the testing team to validate their integration, and with Operations to investigate, troubleshoot, and address production issues.
The candidate should have a minimum of 6+ years of overall IT experience with at least last 3+ years on Java development and frameworks mainly Spring.
Experience developing and managing RESTful API applications using microservices.
Demonstrable Experience in developing scalable Cloud based services on AWS/Azure.
Experience in Javascript, React would be an added advantage.
Experience in SQL and database concepts.
Proficiency in data structures and algorithms.
Experience troubleshooting and finding multiple solutions to a complex problem.
Excellent communication skills and demonstrated ability to effectively communicate technical issues and resolve problems.
Experience in delivering IT solutions using Agile (Scrum) project methodology and practices.

Experience required 6 > Current Experience 5. Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:36:30.292198,2025-07-25 17:36:30.430179,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:36:33.776555,2025-07-25 17:36:33.895079,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:36:37.021218,2025-07-25 17:36:37.227059,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4269656770,https://www.linkedin.com/jobs/view/4269656770,Pending,2025-07-24 20:43:22.712501,2025-07-25 17:43:22.888315,Found a Bad Word in About Job,"
About the job
We are hiring for Full Stack Developer | Gurgaon/Bangalore

Role: Software Engineer - Full Stack
Location: Gurgaon/Bangalore (Hybrid)
Experience-1-3 Years

Mandate Skills- Node JS, Angular, SQL, Java & Spring Boot

Job Description: 
As a Full Stack Developer, you will play a pivotal role in developing and maintaining our web applications (Angular, NodeJS) and backend services (Java, Spring Boot). You will work closely with cross-functional teams to ensure the seamless development and integration of front-end and back-end components, delivering exceptional user experiences. The ideal candidate will have a strong foundation in software development, a keen eye for detail, and a passion for keeping up with emerging technologies.

 Responsibilities:
Collaborate with product managers, UI/UX designers, technical leads, and fellow developers to design and implement robust software solutions.
Participating in daily standup, sprint planning, retrospective meetings during project implementation phase.
Develop responsive and user-friendly front-end interfaces using Angular, ensuring optimal performance across various devices and browsers.
Design and implement RESTful APIs and back-end services using Java/SpringBoot to support front-end functionalities.
Write unit, integration, and end-to-end tests to ensure application quality and performance.
Work with databases and data models to ensure efficient data storage, retrieval, and manipulation.

Skills:
Proven experience (min 1+ years) as a Full Stack Developer with hands-on expertise in Angular, NodeJS and Java with Spring Boot.
Familiarity with the Angular framework and design/architectural patterns (e.g. Microservices, Model-View-Controller (MVC) and Entity framework)
Strong understanding of web development fundamentals, including HTML, CSS, and JavaScript.
Proficiency in designing and consuming RESTful APIs.
Solid knowledge of database systems, SQL, and data modeling.
Familiarity with version control systems (e.g., Git) and agile development methodologies.
Strong problem-solving skills and the ability to work effectively in a collaborative team environment.
Familiarity with Docker, Kubernetes,
Exposure to testing frameworks like Jasmine, Karma (Angular), JUnit, Mockito (Java).
Excellent communication skills, both written and verbal.
Experience using IDE tools like Visual Studio Code and Eclipse.
Self-driven, collaborative, and passionate about clean, maintainable code.

Education and Certification
Bachelor’s degree in Computer Science, Engineering, or related field.
Certification in AWS Certified: Developer Associate or equivalent is a plus.

Interested candidates can send their <NAME_EMAIL>

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4270070355,https://www.linkedin.com/jobs/view/4270070355,Pending,2025-07-21 17:43:27.941454,2025-07-25 17:43:28.029329,Required experience is high,"
About the job
We are urgently hiring for FSD Coveo Search Engineer, details are below-

Experience- 7 to 10 Years
Location- Bangalore
Budget- As per industry standard
Mandatory Skills- Java, React/Angular, Coveo Cloud and Python Scripting

Job details-

Advanced expertise in Coveo platform administration, configuration, and development, with ability to serve as a subject matter expert
Computer Science, Engineering, or equivalent experience, plus 7 years of relevant work experience
Strong Java and JavaScript skills, with experience in Spring framework and front-end frameworks like React or Angular
Proven experience with Coveo search and relevance platforms, including query pipelines and machine learning features
Hands-on experience with Apache Kafka for building and managing event-driven architectures
Proficiency in writing Python scripts for automation, validation, and testing of data pipelines and search indices
Familiarity with agile methodologies and DevOps practices
Exceptional problem-solving and analytical skills for resolving complex technical issues
Strong verbal and written communication skills for collaborating with global teams

Experience required 7 > Current Experience 6. Skipping this job!
",Skipped,Not Available
4275556779,https://www.linkedin.com/jobs/view/4275556779,Pending,2025-07-25 11:43:46.815216,2025-07-25 17:43:47.047187,Found a Bad Word in About Job,"
About the job
Job Title : C/C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions: 
Work on Linux-based platforms and understand open-source processes. 
Solve complex problems using strong troubleshooting skills. 
Communicate and collaborate effectively, both verbally and in writing. 
Handle ambiguity and prioritize tasks effectively. 
Define problems, analyze facts, and develop logical solutions. 
Foster teamwork and resolve issues positively. 

Qualifications: 
Experience Range: 2 to 3 years 
 Skills Required: 
Programming Languages: C/C++. 
Platform: Linux 
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts. 
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions. 
Security: TLS, mTLS, certificate management, and ciphers. 
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP. 
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS. 
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:43:50.986804,2025-07-25 17:43:51.132959,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-21 17:43:56.611241,2025-07-25 17:43:56.757225,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:44:32.227677,2025-07-25 17:44:32.364296,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4272017436,https://www.linkedin.com/jobs/view/4272017436,Previous resume,2025-07-25 13:46:42.964126,2025-07-25 17:48:05.156703,Problem in Easy Applying,"Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.169)
Stacktrace:
	GetHandleVerifier [0x0x7ff6950fe935+77845]
	GetHandleVerifier [0x0x7ff6950fe990+77936]
	(No symbol) [0x0x7ff694eb9cda]
	(No symbol) [0x0x7ff694e921a1]
	(No symbol) [0x0x7ff694f3fc6e]
	(No symbol) [0x0x7ff694f60432]
	(No symbol) [0x0x7ff694f386a3]
	(No symbol) [0x0x7ff694f01791]
	(No symbol) [0x0x7ff694f02523]
	GetHandleVerifier [0x0x7ff6953d684d+3059501]
	GetHandleVerifier [0x0x7ff6953d0c0d+3035885]
	GetHandleVerifier [0x0x7ff6953f0400+3164896]
	GetHandleVerifier [0x0x7ff695118c3e+185118]
	GetHandleVerifier [0x0x7ff69512054f+216111]
	GetHandleVerifier [0x0x7ff6951072e4+113092]
	GetHandleVerifier [0x0x7ff695107499+113529]
	GetHandleVerifier [0x0x7ff6950ee298+10616]
	BaseThreadInitThunk [0x0x7ff99ab1e8d7+23]
	RtlUserThreadStart [0x0x7ff99c43c34c+44]
",Easy Applied,Not Available
4272057441,https://www.linkedin.com/jobs/view/4272057441,Pending,2025-07-25 16:51:21.097716,2025-07-25 17:51:21.663337,Required experience is high,"
About the job
Job Opportunity Flex PLM Technical Architect at ITC Infotech 🌟


Location: Bangalore / PAN India
Experience Required: 8-15 Years
Job Type: Full-Time
Notice period: Immediate to 60 days only.


Job Title
Flex PLM Technical Architect
Job Description
 Good experience in FlexPLM 
 Experienced in understanding client architecture and enterprise landscape
Good experience in REST APIs
Good experience in javascript, JSON and xml file reading
Gathering business requirements, providing solutions and estimations, documenting them.
 Development to implement the solution in FlexPLM, defect fixes.
Development and support on FlexPLM Enhancements, Bugfixes, Change Requests, Customizations, workflows, reports, RCA.
PLM to downstream integration development and System and Application maintenance. Build deployment, Application Refresh, Rehosting.
 End-User trainings, Application Upgrade activities, Installation of CPS and patches.
 Engage with customer through daily/weekly customer meetings.
 Work on live Support and implementation projects and to support customer across time zones if needed.
Good domain knowledge on FlexPLM for Apparel, Footwear and Retail projects both functional and technical.
Excellent Java and FlexPLM development skills
 Understanding of Database concepts and working knowledge of Oracle/SQL
 Capable of Translating client¿s business requirements and objectives into technology solutions and roadmaps. Capable of developing end to end business solutions.
Exposure to working with top retailers, brands in the above domains in FlexPLM developer role.
 Excellent communication, analytical and inter-personal skills. Must be a team player and proactive in building initiatives for the team/practice.
Working knowledge of various models of SDLC.
 Understanding the business requirements specified by the Client.


Kindly share the below details along with your updated resume.

Total Exp:
Rel Exp:
Skill:
Current company with Payroll:
CTC:
ECTC:
NP:
LWD:
Current location:
Preferred location for Bangalore(Yes/No):
Holding any Offers and Package:

Experience required 8 > Current Experience 6. Skipping this job!
",Skipped,Not Available
4269656770,https://www.linkedin.com/jobs/view/4269656770,Pending,2025-07-24 20:52:00.330738,2025-07-25 17:52:00.530737,Found a Bad Word in About Job,"
About the job
We are hiring for Full Stack Developer | Gurgaon/Bangalore

Role: Software Engineer - Full Stack
Location: Gurgaon/Bangalore (Hybrid)
Experience-1-3 Years

Mandate Skills- Node JS, Angular, SQL, Java & Spring Boot

Job Description: 
As a Full Stack Developer, you will play a pivotal role in developing and maintaining our web applications (Angular, NodeJS) and backend services (Java, Spring Boot). You will work closely with cross-functional teams to ensure the seamless development and integration of front-end and back-end components, delivering exceptional user experiences. The ideal candidate will have a strong foundation in software development, a keen eye for detail, and a passion for keeping up with emerging technologies.

 Responsibilities:
Collaborate with product managers, UI/UX designers, technical leads, and fellow developers to design and implement robust software solutions.
Participating in daily standup, sprint planning, retrospective meetings during project implementation phase.
Develop responsive and user-friendly front-end interfaces using Angular, ensuring optimal performance across various devices and browsers.
Design and implement RESTful APIs and back-end services using Java/SpringBoot to support front-end functionalities.
Write unit, integration, and end-to-end tests to ensure application quality and performance.
Work with databases and data models to ensure efficient data storage, retrieval, and manipulation.

Skills:
Proven experience (min 1+ years) as a Full Stack Developer with hands-on expertise in Angular, NodeJS and Java with Spring Boot.
Familiarity with the Angular framework and design/architectural patterns (e.g. Microservices, Model-View-Controller (MVC) and Entity framework)
Strong understanding of web development fundamentals, including HTML, CSS, and JavaScript.
Proficiency in designing and consuming RESTful APIs.
Solid knowledge of database systems, SQL, and data modeling.
Familiarity with version control systems (e.g., Git) and agile development methodologies.
Strong problem-solving skills and the ability to work effectively in a collaborative team environment.
Familiarity with Docker, Kubernetes,
Exposure to testing frameworks like Jasmine, Karma (Angular), JUnit, Mockito (Java).
Excellent communication skills, both written and verbal.
Experience using IDE tools like Visual Studio Code and Eclipse.
Self-driven, collaborative, and passionate about clean, maintainable code.

Education and Certification
Bachelor’s degree in Computer Science, Engineering, or related field.
Certification in AWS Certified: Developer Associate or equivalent is a plus.

Interested candidates can send their <NAME_EMAIL>

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4270070355,https://www.linkedin.com/jobs/view/4270070355,Pending,2025-07-21 17:52:07.560400,2025-07-25 17:52:07.756267,Required experience is high,"
About the job
We are urgently hiring for FSD Coveo Search Engineer, details are below-

Experience- 7 to 10 Years
Location- Bangalore
Budget- As per industry standard
Mandatory Skills- Java, React/Angular, Coveo Cloud and Python Scripting

Job details-

Advanced expertise in Coveo platform administration, configuration, and development, with ability to serve as a subject matter expert
Computer Science, Engineering, or equivalent experience, plus 7 years of relevant work experience
Strong Java and JavaScript skills, with experience in Spring framework and front-end frameworks like React or Angular
Proven experience with Coveo search and relevance platforms, including query pipelines and machine learning features
Hands-on experience with Apache Kafka for building and managing event-driven architectures
Proficiency in writing Python scripts for automation, validation, and testing of data pipelines and search indices
Familiarity with agile methodologies and DevOps practices
Exceptional problem-solving and analytical skills for resolving complex technical issues
Strong verbal and written communication skills for collaborating with global teams

Experience required 7 > Current Experience 6. Skipping this job!
",Skipped,Not Available
4275556779,https://www.linkedin.com/jobs/view/4275556779,Pending,2025-07-25 11:52:12.009917,2025-07-25 17:52:12.269414,Found a Bad Word in About Job,"
About the job
Job Title : C/C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions: 
Work on Linux-based platforms and understand open-source processes. 
Solve complex problems using strong troubleshooting skills. 
Communicate and collaborate effectively, both verbally and in writing. 
Handle ambiguity and prioritize tasks effectively. 
Define problems, analyze facts, and develop logical solutions. 
Foster teamwork and resolve issues positively. 

Qualifications: 
Experience Range: 2 to 3 years 
 Skills Required: 
Programming Languages: C/C++. 
Platform: Linux 
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts. 
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions. 
Security: TLS, mTLS, certificate management, and ciphers. 
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP. 
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS. 
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4270976951,https://www.linkedin.com/jobs/view/4270976951,Pending,2025-07-23 17:52:33.365720,2025-07-25 17:52:33.533141,Found a Bad Word in About Job,"
About the job
About us:
Pixis is a US-based codeless technology company that develops accessible AI to empower brands to scale their performance marketing efforts and augment their decision-making seamlessly. Since its inception, Pixis has been on a mission to develop powerful AI infrastructure that equips marketers across countries with robust plug-and-play AI products, 200+ proprietary, self-evolving AI models without having to write a single line of code. 

The company has raised a total funding of $209M across Series A, B, C and C1, and is backed by recognized investors including SoftBank Vision Fund 2, Touring Capital, Grupo Carso, General Atlantic, Celesta Capital and Chiratae Ventures. 

Our customer base includes global brands such as DHL Express, Joe & The Juice, Claroshop, Allbirds, L’Oreal, HDFC Bank, Skoda, Swiggy, Clar and SmartAsset, to name a few. Today Pixis’ talented and diverse team of 300+ spread across over 14 geographies is focused on building incredibly transformative AI products to help customers get the most out of their marketing and demand generation efforts. 

Get ready to embark on an AI venture at https://pixis.ai/

Why Pixis? 
We at Pixis believe that nothing is impossible, when you fail fast you learn faster, zero hierarchy, put the team above everything else, get constructive feedback that helps you build better products, and disagree if you disbelieve. These values guide us in everything we do, and is reflected in our employees and the products we build together. 
Our commitment to fostering an exceptional workplace has been recognized with the prestigious People Workplace Awards 2024 by the HR Association of India, a testament to our dedication to creating an outstanding environment for our employees.
Join Pixis and be a part of a team where innovation knows no bounds, every idea counts, and together, we shape the future of technology.

Key Responsibilities:
As a Generative AI Engineer at Pixis, you will be at the forefront of designing and implementing next-generation AI models that drive real business impact. Your contributions will directly shape our suite of AI-powered products used by industry-leading enterprises. 

Model Development & Research:
Design, develop, and deploy state-of-the-art generative AI models (e.g., GANs, VAEs, Transformers) tailored for enterprise applications.
Stay updated with the latest research trends and innovations in generative AI to continually enhance our solutions.

Integration & Collaboration:
Collaborate with cross-functional teams (data scientists, software engineers, and product managers) to integrate AI models into our SaaS platforms.
Work closely with product teams to translate business requirements into scalable AI features.

Optimization & Deployment:
Perform data preprocessing, feature engineering, and model tuning to ensure high-quality and efficient AI outputs.
Optimize model performance through rigorous testing, hyperparameter tuning, and evaluation using relevant metrics.
Support the deployment and maintenance of AI solutions in production environments.
Implementing feedback mechanisms (collect user input logs, refine model responses, etc.)

Documentation & Best Practices:
Maintain comprehensive documentation of AI models, methodologies, and experiments.
Contribute to the development and refinement of coding standards, testing procedures, and best practices for AI/ML development.

Requirements & Skills:

Educational Background:
Bachelor’s or Master’s degree in Computer Science, Artificial Intelligence, Machine Learning, or a related field.

Professional Experience:
2–4 years of proven, hands-on experience in generative AI 
Demonstrated ability to develop and deploy state-of-the-art generative models using frameworks like TensorFlow or PyTorch.

Technical Prowess:
Natural Language Processing (NLP): Experience with LLMs (e.g., GPT, BERT) and conversational AI frameworks.
Machine Learning & Generative AI: Familiarity with model training, fine-tuning, and deployment.
API Integration: Ability to integrate third-party AI services
Experience building or integrating LLM-based chatbots (e.g., designing prompts, context management, multi-turn conversations) is a plus.
Familiarity with payment integrations and transaction flows is a plus

Soft Skills:
A proactive, innovative mindset paired with a collaborative spirit.
Exceptional problem-solving skills and the ability to articulate complex ideas clearly.
Passion for staying ahead of technological trends and driving forward-thinking solutions.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4272031343,https://www.linkedin.com/jobs/view/4272031343,Pending,2025-07-25 15:52:41.019333,2025-07-25 17:52:41.553939,Found a Bad Word in About Job,"
About the job
Senior Software Engineer - Backend
FinacPlus provide virtual business process services to overseas clients and this position is to be part of the dedicated team which providesmortgage back-office servicesand software productdevelopment to Toorak Capital Partners https://www.toorakcapital.com/ from Mortgage FinanceIndustry.

About Toorak Capital

Toorak Capital Partnersis an integrated lending platformthat funds small business purposeresidential, multifamily and mixed-use loans throughout the U.S. and the United Kingdom.

Headquartered in Summit, N.J., Toorak Capital Partners acquires and manages loans directly from private lendersthat originate high credit quality loans.

RESPONSIBILITIES
To Work as Backend Developerin developing Cloud Based Web Applications
To be part of team working on various types of web applications related to MortgageFinance.
Experience in solving a real-world problem of Implementing, Designing and helping develop a new Enterprise class Product from ground-up.
You have expertise in the AWS Cloud Infrastructure and Micro-services architecture around the AWS Service stack like Lambdas, SQS, SNS, MySQL Databases along with Dockers and containerized solutions/applications.
Experienced in Relational and No-SQL databases and scalable design.
Experience in solvingchallenging problems by developing elegant,maintainable code.
Delivered rapid iterations of software based on user feedback and metrics.
Help the team make key decisions on our productand technology direction.
You actively contribute to the adoptionof frameworks, standards, and new technologies.

QUALIFICATIONS AND EXPERIENCE
Overall 5 to 7 years of experience. Node.js experience is must.
At least 3+ years of experience or couple of large-scale productsdelivered on microservices.
Strong design skillson microservices and AWS platforminfrastructure.
Excellent programming skill in Python,Node.js and Java script.
Hands on development in rest API’s.
Good understanding of nuances of distributed systems,scalability, and availability.

Location: Bangalore
Timings: 11am To 8pm IST
Salary Range: Best in Industry
Send Application To: <EMAIL>

Contains bad word ""Senior Software Engineer"". Skipping this job!
",Skipped,Not Available
4270239677,https://www.linkedin.com/jobs/view/4270239677,Pending,2025-07-21 17:52:48.163829,2025-07-25 17:52:48.438834,Found a Bad Word in About Job,"
About the job
Position - Frontend - Mobile RN Developer

Experience - 5+ Years

Location - Multiple locations across India. Here are some of the key locations:
Bangalore
Bhubaneswar
Chennai
Coimbatore
Gandhinagar
Gurugram
Hyderabad
Kolkata
Mumbai
Noida
Pune
Salem
Tiruchirappalli

Must Have Skills - Native module , Axios , Custom hooks , Husky , SonarQube , Redux , info.plist , manisfest.xml 

Must Have:
Expertise in strategizing and developing mobile applications for both iOS and Android platforms using React Native.
5+ years of hands-on experience in building and designing mobile applications with React Native - TypeScript.
Strong proficiency with Redux-Saga for managing application state and handling side effects.
Solid knowledge of JavaScript, TypeScript, Swift, and Kotlin for cross-platform and native mobile development.
Experience integrating third-party libraries into React Native apps, such as Firebase, Sentry, and others for push notifications, analytics, and crash reporting.
Ability to build and design reusable NPM packages for multiple projects, promoting efficient code sharing.
Proven experience developing custom native modules for at least one platform (iOS with Swift/Obj-C or Androidwith Java/Kotlin).
Proficient in creating React Native components that are efficient, maintainable, and easy to test.
Strong knowledge of unit testing and writing test cases using Jest to ensure high code quality.
Version control using Git to maintain a clean and organized codebase.
Experience working with design systems such as Atomic Design or Fabric to maintain consistency across applications.
Familiarity with Figma to translate design specs into well-crafted, functional mobile interfaces.
Comfortable using collaboration tools like JIRA, Confluence, and other project management software to track progress and communicate effectively within teams.

About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 16:52:54.696289,2025-07-25 17:52:54.784914,Found a Bad Word in About Job,"
About the job
Type: Contract (C2H)
Duration: 6 Months 
Location: Bangalore (Hybrid 3 Days )
Rate: 18 LPA
Experience: 6 to 9 Years

Looking for an experienced Hyperion support Developer with 5-8 years of relevant experience. Candidates with financial background is preferred. The detailed responsibilities are mentioned below.
HFM, Planning, Essbase, HPCM
Perform coding and configuration to enhance and maintain Oracle EPM tools or Hyperion applications, including Planning, Essbase (BSO and ASO cubes), FDMEE,HPCM.
Monitor, maintain security, management process controls, task flows, business rules, scripts, member lists, journal module, objects (Webforms, Grids, Task lists), consolidation and data clearing procedures, metadata updates etc.
Strong knowledge of Essbase scripting and Calc Manager Rules, Essbase Calc Scripts, Batch scripts, MDX & MAXL. EPM solution, Essbase Cubes.
Work closely with Master data team and finance teams to manage metadata changes, business rule updates, form/dashboard enhancements, and data loads.
Have good general functional knowledge and understanding of budgeting, forecasting and financial analysis and close processes
Sound knowledge of Life Cycle Management, user Management, Security Management etc
Provide level one and level two support in line with the team’s remit: CoA validations and deployments using EPMA / DRM; User support and setups.
Good analytical, problem solving, & communication skills
Have experience working with ServiceNow ticketing tool, approval process and SLA.
Address user queries with webforms, SmartView, data reconciliation in HFM
 Read, understand and update HFM rules per user requirements
 Update HFM application metadata and security as required
 Re-shell application and reconcile data for regression testing before monthly updates.
 Deploy updates to Pre-Prod and reconcile data before monthly updates in HFM and Planning/Essbase apps.
 Deploy changes to Production after preproduction COA and new enhancements testing.
 FDMEE: Monitor data loading schedule jobs and if any failures/kickouts. Address these by liaising with entity owners (in the case of data quality errors) or updating dimension maps

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4270223783,https://www.linkedin.com/jobs/view/4270223783,Pending,2025-07-21 17:52:59.796523,2025-07-25 17:52:59.892493,Found a Bad Word in About Job,"
About the job
Job Title: Fullstack Developer

Experience Required: 2–4 years

Location: Bengaluru (Hybrid) (3 days WFO, 2 days WFH)

Employment Type: Full-time

Key Responsibilities

Design, develop, test, and maintain scalable fullstack applications using modern technologies.
Implement responsive and cross-browser compatible UI components with HTML, CSS (SASS/LESS), and JavaScript.
Develop robust backend services and APIs using Node.js, Express.js, JavaScript/TypeScript.
Collaborate with cross-functional teams including designers, product managers, and other developers.
Ensure code quality through best practices, including unit testing and code reviews.
Contribute to architectural decisions and provide innovative solutions to complex challenges.

Must-Have Qualifications

2–3 years of professional experience as a Fullstack Engineer.
Strong proficiency in HTML, CSS (LESS/SASS), and JavaScript with a deep understanding of responsive and cross-browser web design principles.
Hands-on experience with modern front-end frameworks such as React or Angular.
Solid backend development experience in Node.js, Express.js, and either JavaScript or TypeScript.
Strong grasp of software engineering fundamentals, including data structures, algorithms, and problem-solving skills.

Good-to-Have Skills

Experience building applications that integrate AI tools or AI-driven workflows.
Exposure to backend development using Python or Java.
Knowledge of databases, including MongoDB, DynamoDB, MySQL, Redis, ElastiCache, and ElasticSearchDB.
Experience designing and developing RESTful APIs, preferably with metric-driven API Gateway integrations.
Familiarity with AWS services, Kubernetes, microservices, and domain-driven architecture.
Excellent written and verbal communication skills, with the ability to clearly present technical concepts to stakeholders and team members.

Skills: angular,typescript,html,node.js,express.js,javascript,react,css,restful apis,css (sass/less)

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275590038,https://www.linkedin.com/jobs/view/4275590038,Pending,2025-07-25 15:53:21.819786,2025-07-25 17:53:21.970279,Found a Bad Word in About Job,"
About the job
Job Title: Senior Software Architect
Location: India (Remote/Hybrid)
Experience: 5–8 years
Education: BS/MS in Computer Science or equivalent

About the Role
We’re seeking a high-impact Senior Software Architect to design, lead, and deliver core product features and scalable systems. In this role, you will mentor engineers, drive technical excellence, and own the full software development lifecycle — from design to deployment.

Key Responsibilities
Architect and design scalable, distributed systems for high-performance cloud environments.
Lead critical product features and core infrastructure projects.
Mentor and guide engineers through design/code reviews and best practices.
Own end-to-end SDLC: from requirements gathering to design, development, testing, deployment, and support.
Collaborate across global teams to deliver innovative and reliable software solutions.
Continuously improve system performance, scalability, and resilience.

Technical Expertise
Strong background in Java, Big Data, and Distributed Systems.
Proficient in Microservices Architecture and Cloud platforms (AWS, Azure).
Deep knowledge of Algorithms, Data Structures, and system design principles.
Hands-on experience with NoSQL databases, Hadoop ecosystem (HDFS, Hive, etc.).

What We’re Looking For
5–8 years of experience in software engineering, including system architecture and cloud-based design.
A proven track record of driving complex projects from concept to production.
Passion for mentoring, technical leadership, and continuous improvement.
Excellent communication and collaboration skills in a fast-paced, global team environment.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
4270157274,https://www.linkedin.com/jobs/view/4270157274,Pending,2025-07-25 13:53:27.520548,2025-07-25 17:53:27.663937,Found a Bad Word in About Job,"
About the job
Job description

We are looking for an experienced Android Developer to join our dynamic team. The ideal candidate will be responsible for designing, developing, and deploying Android applications to deliver innovative and intelligent user experiences.

Key Responsibilities:

Develop and maintain Android applications using Android Studio.
Support and debug cross-platform applications with Xcode (for iOS builds).
Collaborate with designers and product managers to create intuitive, user-friendly mobile experiences.
Integrate third-party APIs and SDKs.
Ensure performance, quality, and responsiveness of applications.
Write clean, maintainable code following best practices (MVVM, Clean Architecture, etc.).
Manage code using version control systems like Git.
Conduct testing and debugging across multiple device types and OS versions.
Participate in code reviews and contribute to team knowledge sharing.

Qualifications:

Bachelors or Masters degree in Computer Science, Engineering, or a related field.
2 years of experience as Andriod Developer
Experience in Android app development using Kotlin and/or Java,
Proficient in integrating RESTful APIs and third-party SDKs.
Strong understanding of Android UI/UX principles, design patterns (MVVM, MVP, etc.)
Proficiency in Android Studio.
Familiarity with Xcode and the iOS build/debug process.
Understanding of mobile UI/UX principles and best practices.
Experience consuming RESTful APIs and working with JSON.
Familiar with version control tools such as Git.
Experience with modern architecture patterns (MVVM, Clean Architecture).

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275601334,https://www.linkedin.com/jobs/view/4275601334,Pending,2025-07-25 16:53:31.430681,2025-07-25 17:53:31.677764,Found a Bad Word in About Job,"
About the job
About Us
MyRemoteTeam, Inc is a fast-growing distributed workforce enabler, helping companies scale with top global talent. We empower businesses by providing world-class software engineers, operations support, and infrastructure to help them grow faster and better.

Job Title: Full Stack Developer – Java + React
Experience: 5+ Years

Job Summary:
We are looking for a talented and experienced Full Stack Developer (Java + React) with a strong background in building scalable, high-quality, and high-performance web applications. The ideal candidate will have hands-on experience in Java, Spring Boot, Microservices architecture, and React.js.

Key Responsibilities:
Design, develop, and maintain scalable backend services using Java, Spring Boot, and Microservices.
Develop intuitive and responsive frontend interfaces using React.js.
Collaborate with product managers, UI/UX designers, and other developers to understand requirements and translate them into technical solutions.
Ensure code quality through unit testing, code reviews, and best practices.
Integrate APIs and work with third-party services as needed.
Optimize application performance and scalability.
Participate in Agile/Scrum development cycles, including sprint planning and daily stand-ups.
Troubleshoot and resolve production issues in a timely manner.

Required Skills:
5+ years of experience in Java development
Strong experience with Spring Boot and building RESTful APIs
Solid understanding of Microservices architecture
2+ years of hands-on experience with React.js, JavaScript, HTML5, and CSS3
Experience working with databases like MySQL, PostgreSQL, or MongoDB
Familiarity with version control systems (Git)
Understanding of CI/CD pipelines and deployment processes
Good problem-solving skills and attention to detail

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4275582302,https://www.linkedin.com/jobs/view/4275582302,Pending,2025-07-25 14:53:41.516523,2025-07-25 17:53:41.717761,Found a Bad Word in About Job,"
About the job
Job Title : C++ Developer
Location State : Karnataka
Location City : Bangalore
Experience Required : 2+ Year(s)
Shift: General
Work Mode: Hybrid
Position Type: Contract

Company Name: VARITE INDIA PRIVATE LIMITED

About The Client: An American multinational digital communications technology conglomerate corporation headquartered in San Jose, California. The Client develops, manufactures, and sells networking hardware, software, telecommunications equipment, and other high-technology services and products. The Client specializes in specific tech markets, such as the Internet of Things (IoT), domain security, videoconferencing, and energy management. It is one of the largest technology companies in the world, ranking 82nd on the Fortune 100 with over $51 billion in revenue and nearly 83,300 employees.

Essential Job Functions:
Work on Linux-based platforms and understand open-source processes.
Solve complex problems using strong troubleshooting skills.
Communicate and collaborate effectively, both verbally and in writing.
Handle ambiguity and prioritize tasks effectively.
Define problems, analyze facts, and develop logical solutions.
Foster teamwork and resolve issues positively.

Qualifications:
Experience Range: 2 to 3 years

Skills Required:
Programming Languages: C/C++.
Platform: Linux
Core Concepts: Multithreading, Singleton, Algorithms, Data Structures, Object-Oriented Design, and Database concepts.
Asynchronous Transactions: REST API, Event-driven patterns, IPC, and HTTP transactions.
Security: TLS, mTLS, certificate management, and ciphers.
Networking Fundamentals: IP, TCP/UDP, DNS, HTTP.
Troubleshooting: Debugging functional, scale, and threading issues in Linux OS.
Agile Experience: Familiarity with Agile development environments.

How to Apply: Interested candidates are invited to submit their resume using the apply online button on this job post.

Equal Opportunity Employer: VARITE is an equal opportunity employer. We celebrate diversity and are committed to creating an inclusive environment for all employees. We do not discriminate on the basis of race, color, religion, sex, sexual orientation, gender identity or expression, national origin, age, marital status, veteran status, or disability status.

Unlock Rewards: Refer Candidates and Earn. If you're not available or interested in this opportunity, please pass this along to anyone in your network who might be a good fit and interested in our open positions. VARITE offers a Candidate Referral program, where you'll receive a one-time referral bonus based on the following scale if the referred candidate completes a three-month assignment with VARITE.

Exp Req - Referral Bonus
0 - 2 Yrs. - INR 5,000
2 - 6 Yrs. - INR 7,500
6 + Yrs. - INR 10,000

About VARITE: VARITE is a global staffing and IT consulting company providing technical consulting and team augmentation services to Fortune 500 Companies in USA, UK, CANADA and INDIA. VARITE is currently a primary and direct vendor to the leading corporations in the verticals of Networking, Cloud Infrastructure, Hardware and Software, Digital Marketing and Media Solutions, Clinical Diagnostics, Utilities, Gaming and Entertainment, and Financial Services.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4268786150,https://www.linkedin.com/jobs/view/4268786150,Pending,2025-07-19 17:53:46.328710,2025-07-25 17:53:46.550691,Found a Bad Word in About Job,"
About the job
Java backend developer 
 Job Summary
We’re looking for a Java backend developer to support our team in Bangalore .This role offers the opportunity to work on meaningful projects, collaborate with talented colleagues, and contribute to the success of a growing company. If you’re someone who takes initiative, values continuous learning, and thrives in a collaborative setting, we’d love to hear from you.
Role Description

Experience in building Cloud Native applications from a Domain driven design and micro-services architecture perspective
Expertise in defining physical data models, write maintainable & testable code that is consistent with micro-service architecture principles
Experience in working with fully automated CI / CD pipelines, support software solutions that are customer focused & highly secure.
Know-how in defining end-to-end application architecture/development, Performance, Security, testing.
Must Have Skills: 
Hands-on Experience in Micro Services Design and Development - adhering to Azure Cloud principles.
Implementation knowledge of managing asynchronous communication using Kafka or other JMS broker solutions to achieve similar result.
Hands on experience with coding large-scale feature sets at every level of the stack, from the database till APIs.
Java (17 and above),
Spring boot,
Spring-core,
Hibernate,
RESTful,
GraphQL,
Azure AKS
Maven,
GIT, Azure Repo
mockito, jmockit, jacoco, etc
This role requires a wide variety of strengths and capabilities, including:
BS/BA degree or equivalent experience
Ability to work collaboratively in teams and develop meaningful relationships to achieve
Understanding of software skills such as business analysis, development, maintenance, and software improvement.
RDBMS experience preferably with Postgres/Mysql
Strong believer of code quality and Well versed with Test Driven Development/Behaviour
Exposure/competence with Agile Development approach
Working experience with continuous integration/development using CI/CD
 Please send the update CV along with the following <NAME_EMAIL>
If you are not interested, please refer to your friends.

Full Name:
Current Location:
Visa Status:
Total years of experience:
Relevant years of experience:
Current salary:
Expected Salary:
Notice period:
Reason for leaving:


About CLPS RiDiK
RiDiK is a global technology solutions provider and a subsidiary of CLPS Incorporation (NASDAQ: CLPS), delivering cutting-edge end-to-end services across banking, wealth management, and e-commerce. With deep expertise in AI, cloud, big data, and blockchain, we support clients across Asia, North America, and the Middle East in driving digital transformation and achieving sustainable growth. Operating from regional hubs in 10 countries and backed by a global delivery network, we combine local insight with technical excellence to deliver real, measurable impact. Join RiDiK and be part of an innovative, fast-growing team shaping the future of technology across industries.

Thanks & Regards,
Rituparna Das
IT Recruiter
CLPS Inc.
| India HP/Whatsapp: +91 **********
| India Office: + 65 ********

Contains bad word ""Blockchain"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 11:54:41.449099,2025-07-25 17:54:41.732766,Found a Bad Word in About Job,"
About the job
Life on the team

Join a dynamic supportive team working together to solve strong technical challenges by building high-quality ServiceNow solutions. Established as one of the first ServiceNow partners in Europe and awarded the 2022 EMEA Elite Segment Partner of the Year, we have grown a team with strong expertise across every aspect of the Now Platform. Our customers look to us for advice, best practice, and well-designed implementations. As well as to solve enterprise-wide process challenges by bringing great user experiences. We are expanding our team of Senior Technical Consultants and are seeking individuals who want to grow with us and progress to become Technical Architects. Through real-world experience and participation in the Certified Master Architect and Certified Technical Architect programs, we offer abundant opportunities for our team members to develop their technical skills and gain client-facing experience.

What you’ll do

Delivering high quality ServiceNow implementations based on business process requirements.
Effective delivery of quality solutions using Computacenter's methodologies and ensuring adherence to coding and design standards, and generating technical documentation
Increasing application operating efficiency and adapting to new requirements, as necessary
Keep up to date with current and future market developments, technologies, product, and strategies.
Attend and present at customer meetings to ensure understanding of customer requirements and to assist with knowledge transfer.
Recording, qualification and questioning of customer requirements, even in complex projects and in the case of unclear customer requirements and standards.
Successful handover of technology to internal or customer support function
we’ll support you to Attain and retain ServiceNow Certifications and partner accreditations.

What you’ll need

8+ years of experience in the ServiceNow Application Development
Configure and customize ServiceNow applications and modules using scripting (JavaScript), workflow, and other development techniques. 
Proven experience in configuring and customizing the ServiceNow platform and Expertise on 4-5 applications ranging from FSM & CSM, SPM, ITAM, Now App Engine and Portals.
Ability to provide technical leadership and support to Business Process Consultants. 
Responsible for leading technical implementations, providing technical design lead and mentoring junior members of the team.
Hands on Skills and/or experience in Web Technologies (e.g., Javascript, SOAP/REST web services, XML & JSON, Angular.js, Seismic)
Product line (CIS) accreditation preferred – Certified or pursuing.
Understanding of Software Development Lifecycle experience in Agile projects
Open and friendly personality, with ability to be customer facing. 
Be self-managing and capable of working alone or as part of a team. 

Experience & Education:

BE / BTech/MCA in Computer Science or related disciple 
Strong knowledge of JavaScript, scripting, and web development.
Familiarity with ITSM processes and ITIL framework.

Certifications

ServiceNow Certified Application Developer
ServiceNow Certified Implementation Specialist - CSM, SPM, ITAM, ITBM and others

About us

With over 20,000 employees across the globe, we work at the heart of digitisation, advising organisations on IT strategy, implementing the most appropriate technology, and helping our customers to source, transform and manage their technology infrastructure in over 70 countries. We deliver digital technology to some of the world’s greatest organisations, driving digital transformation, and enabling people and their businesses. 

Learning and development

Our people are our strength which is why we offer leadership training, coaching, mentoring, professional development, and international opportunities. Whichever direction you choose to go in – whether it’s a well-trodden path or a completely new part of the business.

You belong.

We passionately believe in the power of diversity and inclusion. We celebrate our differences because we know a diverse workforce with different experiences and perspectives helps us win together. And to do that, you need to feel comfortable to bring your whole self to work – and you can only do that when you feel supported, valued, and have a sense of belonging which is what we strive to achieve. Your application is considered on its merits regardless of your age, disability, ethnicity, faith, gender identity or sexual orientation. All that matters to us is that you share our vision and our values, and that you bring the experience and skills we need. We are proud to be a Disability Confident Employer, we welcome applications from people with a disability – and guarantee to interview applicants who have a disability and meet the essential requirements for the job.

Contains bad word ""Technical Lead"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-23 17:55:31.660736,2025-07-25 17:55:31.960926,Found a Bad Word in About Job,"
About the job
Onsurity is a rapidly growing employee healthcare benefits platform that provides flexible and customised healthcare subscriptions for SMEs, start-ups, and enterprises. We believe that access to healthcare benefits shouldn’t be a luxury. It is this philosophy that has strengthened our commitment towards making healthcare affordable and accessible for all.
Our subscriptions include discounts on medicine orders, health checkups, fitness plans, free doctor teleconsultations, and insurance, among other benefits. We believe in inclusivity which means our plans are not limited to full-time employees. We also cover contractual workers, interns, freelancers, and consultants.
We encourage you to read more about us on www.onsurity.com. You can also find us on LinkedIn, Instagram, and YouTube.
Below are stories that define our journey and showcase our commitment to democratizing healthcare across the country.
Onsurity is providing healthcare membership to SMEs with as low as three employees
The Journey Of Startups: Journey Onsurity
Cricketer Anil Kumble backs Onsurity as strategic advisor
Onsurity partners with Gulf Oil to offer healthcare to 10,000 truckers
83% Indian Employees Unaware Of Employer-Provided Healthcare Benefits, Says Study
News: Onsurity secures $45M Series B round led by Creaegis — People Matters

We were also featured in the first season of Disney+ Hotstar's remarkable series, The Great Indian Disruptors.
Our strategic partner and investor, cricketing legend Anil Kumble, is actively involved in our mission to make healthcare more accessible. Anil Kumble recently graced us with his presence at Onsurity’s Bengaluru office and engaged with our employees. He is passionate about our mission and has played an instrumental role in our journey so far.
Recently, Dun & Bradstreet India acknowledged our mission and conferred us with the Dun & Bradstreet Start-up 50 Trailblazer 2023 award.

About the Role:

We are seeking a skilled React Native Developer to join our mobile development team. You will be responsible for building cross-platform mobile applications for iOS and Android using the React Native framework. As part of our tech team, you will collaborate with product managers, designers, and backend developers to deliver high-quality, scalable mobile solutions.

Key Responsibilities:

Develop and maintain cross-platform mobile applications using React Native.
Integrate mobile apps with RESTful APIs and third-party services.
Collaborate with designers to implement intuitive UI/UX designs.
Write clean, maintainable, and well-documented code.
Optimize app performance and troubleshoot issues.
Conduct code reviews and maintain code quality.
Stay up-to-date with emerging technologies and mobile development trends.

Requirements:

2+ years of experience with React Native and JavaScript/TypeScript.
Experience deploying apps to the Apple App Store and Google Play Store.
Solid understanding of mobile architecture, performance optimization, and native device features.
Familiarity with Redux, MobX, or other state management tools.
Experience integrating native modules (e.g., Swift, Objective-C, Java, Kotlin) is a plus.
Understanding of REST APIs, JSON, and offline storage mechanisms.
Familiarity with Git and version control tools.
Excellent problem-solving and communication skills.

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
4273995470,https://www.linkedin.com/jobs/view/4273995470,Pending,2025-07-23 17:55:46.682715,2025-07-25 17:55:47.018731,Found a Bad Word in About Job,"
About the job
Hi,
Please find JD attached with company description for your reference. If you're interested, please fill the below form

https://forms.gle/pYe6pBEMDKGS2LibA
I'll Connect with you soon, once I receive interview slots.

Email - <EMAIL>

Position: Senior Frontend Developer
Experience: 5 to 7 Years
Location: Bangalore (Hybrid/Onsite depending on client requirement)

Key Skills
• Primary: React.js, Redux, Micro Frontend Architecture, JavaScript (ES6+)
• Secondary: Next.js, Tailwind CSS

Job Description

We are seeking a skilled and experienced Senior Frontend Developer with expertise in building scalable, high-performance web applications using React.js and Redux within a Microfrontend architecture. The ideal candidate should be passionate about clean code, performance optimization, and delivering seamless user experiences.

Responsibilities
• Develop and maintain complex front-end applications using React.js and Redux.
• Architect and implement Microfrontend solutions for large-scale web platforms.
• Optimize application for maximum speed, scalability, and responsiveness.
• Integrate RESTful APIs and collaborate closely with backend teams.
• Apply best practices in coding, design, and architecture to ensure code quality.
• Enhance UI/UX using Tailwind CSS and modern design principles.
• Collaborate with cross-functional teams including Product, Design, and QA.
• Mentor junior developers and contribute to code reviews.
• Stay updated with the latest trends in front-end technologies.

Nice to Have
• Experience with Next.js for SSR (Server-Side Rendering) and static site generation.
• Familiarity with CI/CD pipelines for front-end deployment.
• Prior experience working in Agile/Scrum teams.

Qualifications
• 5-7 years of hands-on experience in React.js, Redux, JavaScript.
• Strong understanding of Micro frontend architecture and implementation strategies.
• Proficiency in Next.js and Tailwind CSS is preferred.
• Good problem-solving skills and attention to detail.
• Excellent communication and collaboration skills.
Company Introduction

Bounteous x Accolite makes the future faster for the world’s most ambitious brands. Our services span Strategy, Analytics, Digital Engineering, Cloud, Data & AI, Experience Design, and Marketing. We are guided by Co-Innovation, our proven methodology of collaborative partnership. Bounteous x Accolite brings together 5,000+ employees spanning North America, APAC, and EMEA, and partnerships with leading technology providers. Through advanced digital engineering, technology solutions, and data-driven digital experiences, we create exceptional and efficient business impact and help our clients win.
For more information visit: www.accolite.com

Accolite Digital India Pvt Ltd, is focused on serving Fortune 500 Customers in Healthcare, Banking and Financial Services, Telecommunications and Automotive Verticals. The company is focused on producing the best technical talent in these Verticals and solving most complex technical and business problems. Accolite is a cutting edge information technology services and product development company headquartered in Dallas, Texas, with four development centres in Bangalore, Delhi, Mumbai, Hyderabad and Coimbatore, India.

1. A young IT Services company headquartered in Texas with business Operations in the US, Canada, UK, Middle East, and India. (About 15 years old)
2. In India we have offices in Gurgaon, Hyderabad, Mumbai, Chennai, and Bangalore. In 2021 we have expansion plans to 3 other European and Latin American countries
3. Accolite has a team of 5000+ headcount across its locations.
4. We have recently entered into a Strategic Partnership with New Mountain Capital, a leading growth-oriented investment firm with over $30 billion in assets under management who also own companies like eMids.
5. We have 12+ Fortune 500 clients including some of the biggest Banks and Telecom companies in the world.
6. Accolite had been growing at a 30% + growth rate in at least 3 years and targets to become a half Billion Dollar company in the next 4 years.
7. Open door flexible performance-driven work culture

Accolite Digital is a leading digital transformation services provider that delivers design-led, complex digital transformation initiatives to Fortune 500 clients. Our differentiated services span digital product engineering, cloud and DevOps, data and AI, customer experience, cyber security, and design services. Accolite Digital provides these services to the banking and financial services, insurance, technology, media and telecom, healthcare, and logistics industries. With more than 5000 professionals globally, Accolite has presence across the United States, Canada, Mexico, Europe, and India, with digital labs in Bangalore, Hyderabad, Gurugram, and Chennai.

For more information visit: www.accolite.com

About Bounteous
Founded in 2003 in Chicago, Bounteous is a leading digital experience consultancy that co-innovates with the world’s most ambitious brands to create transformative digital experiences. With services in Strategy, Experience Design, Technology, Analytics and Insight, and Marketing, Bounteous elevates brand experiences and drives superior client outcomes. For more information, please visit www.bounteous.com.
For more information about Co-Innovation, download the Co-Innovation Manifesto at co-innovation.com.
For the most up-to-date news, follow Bounteous on Twitter, LinkedIn, Facebook, and Instagram.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-22 17:57:37.820823,2025-07-25 17:57:38.023019,Found a Bad Word in About Job,"
About the job
About the Company


Bosch Global Software Technologies Private Limited is a 100% owned subsidiary of Robert Bosch GmbH, one of the world's leading global supplier of technology and services, offering end-to-end Engineering, IT and Business Solutions. With over 28,200+ associates, it’s the largest software development center of Bosch, outside Germany, indicating that it is the Technology Powerhouse of Bosch in India with a global footprint and presence in the US, Europe and the Asia Pacific region.


About the Role


We are seeking a highly skilled and experienced Java AWS React Developer to join our dynamic development team. The ideal candidate will have a strong background in Java backend development, cloud-native application design using AWS, and modern front-end development with React.js. You will be responsible for designing, developing, and maintaining scalable web applications that deliver high performance and reliability.


Responsibilities


Design, develop, and maintain scalable and secure backend services using Java (Spring Boot).
Build responsive and dynamic user interfaces using React.js.
Develop and deploy cloud-native applications on AWS using services like Lambda, API Gateway, S3, DynamoDB, ECS, etc.
Collaborate with cross-functional teams including product managers, designers, and QA engineers.
Write clean, maintainable, and efficient code following best practices.
Participate in code reviews, unit testing, and integration testing.
Troubleshoot and resolve technical issues across the stack.
Ensure application performance, uptime, and scale, maintaining high standards of code quality and thoughtful design.


Qualifications


5–8 years of hands-on experience in software development.
Strong proficiency in Java, especially with Spring Boot.
Solid experience with React.js, JavaScript (ES6+), HTML5, and CSS3.
Proficiency in AWS services (EC2, Lambda, S3, RDS, DynamoDB, CloudFormation, etc.).
Experience with RESTful APIs and microservices architecture.
Familiarity with CI/CD pipelines and DevOps practices.
Strong understanding of software engineering principles, design patterns, and best practices.
Excellent problem-solving and communication skills.


Preferred Skills


AWS Certification (e.g., AWS Certified Developer or Solutions Architect).
Experience with containerization tools like Docker and orchestration with Kubernetes.
Familiarity with Agile/Scrum methodologies.
Knowledge of security best practices in cloud and web development.


Qualifications
BE/B.Tech/MCA

Contains bad word ""Product Manager"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-21 17:59:57.171213,2025-07-25 17:59:57.464808,Found a Bad Word in About Job,"
About the job
About Us:

Tejas Networks is a global broadband, optical and wireless networking company, with a focus on technology, innovation and R&D. We design and manufacture high-performance wireline and wireless networking products for telecommunications service providers, internet service providers, utilities, defence and government entities in over 75 countries. Tejas has an extensive portfolio of leading-edge telecom products for building end-to-end telecom networks based on the latest technologies and global standards with IPR ownership. We are a part of the Tata Group, with Panatone Finvest Ltd. (a subsidiary of Tata Sons Pvt. Ltd.) being the majority shareholder. Tejas has a rich portfolio of patents and has shipped more than 900,000 systems across the globe with an uptime of 99.999%. Our product portfolio encompasses wireless technologies (4G/5G based on 3GPP and O-RAN standards), fiber broadband (GPON/XGS-PON), carrier-grade optical transmission (DWDM/OTN), packet switching and routing (Ethernet, PTN, IP/MPLS) and Direct-to-Mobile and Satellite-IoT communication platforms. Our unified network management suite simplifies network deployments and service implementation across all our products with advanced capabilities for predictive fault detection and resolution. As an R&D-driven company, we recognize that human intelligence is a core asset that drives the organization’s long-term success. Over 60% of our employees are in R&D, we are reshaping telecom networks, one innovation at a time.


Why Tejas:
We are on a journey to connect the world with some of the most innovative products and solutions in the wireless and wireline optical networking domains. Would you like to be part of this journey and do something truly meaningful? Challenge yourself by working in Tejas’ fast-paced, autonomous learning environment and see your output and contributions become a part of live products worldwide.

At Tejas, you will have the unique opportunity to work with cutting-edge technologies, alongside some of the industry’s brightest minds. From 5G to DWDM/ OTN, Switching and Routing, we work on technologies and solutions that create a connected society. Our solutions power over 500 networks across 75+ countries worldwide, and we’re constantly pushing boundaries to achieve more. If you thrive on taking ownership, have a passion for learning and enjoy challenging the status quo, we want to hear from you!


About Team:
This team is responsible for Platform and software validation for the entire product portfolio. They will develop automation Framework for the entire product portfolio. Team will develop and deliver customer documentation and training solutions. Compliance with technical certifications such as TL9000 and TSEC is essential for ensuring industry standards and regulatory requirements are met. Team works closely with PLM, HW and SW architects, sales and customer account teams to innovate and develop network deployment strategy for a broad spectrum of networking products and software solutions. As part of this team, you will get an opportunity to validate, demonstrate and influence new technologies to shape future optical, routing, fiber broadband and wireless networks.


Roles & Responsibilities:

Design and implement system solutions, propose process alternatives, and enhance business viewpoints to adopt standard solutions.
Specify and design end-to-end solutions with high- and low-level architecture design to meet customer needs.
Apply solution architecture standards, processes, and principles to maintain solution integrity, ensuring compliance with client requirements.
Develop full-scope solutions, working across organizations to achieve operational success.
Research, design, plan, develop, and evaluate effective solutions in specialized domains to meet customer requirements and outcomes.
Solve complex technical challenges and develop innovative solutions that impact business performance.


Mandatory skills:

Around 3 to 6 Years Strong expertise in Cloud-Native, Microservices, and Virtualization technologies such as Docker, Kubernetes, OpenShift, and VMware.
Experience in Istio or Nginx Ingress, Load balancer, OVS, SRIOV and dpdk etc.
Hands-on experience in creating Kubernetes clusters, virtual machines, virtual networks & bridges in bare metal servers.
Expertise in server virtualization techniques such as VMware, Red Hat OpenStack, KVM.
Solid understanding of cloud concepts, including Virtualization, Hypervisors, Networking, and Storage.
Knowledge of software development methodologies, build tools, and product lifecycle management.
Experience in creating and updating Helm charts for carrier-grade deployments.
Deep understanding of IP networking in both physical and virtual environments.
Implementation of high availability, scalability, and disaster recovery measures.
Proficiency in Python/Shell scripting (preferred).
Experience in automation scripting using Ansible and Python for tasks such as provisioning, monitoring, and configuration management.

Desired skills:

Ability to debug applications and infrastructure to ensure low latency and high availability.
Collaboration with cross-functional teams to resolve escalated incidents and ensure seamless operations on deployed cloud platforms.

Preferred Qualifications:

Bachelor’s or Master’s degree in Computer Science, Information Technology, or a related field.
Certifications in Kubernetes (CKA/CKS) Or OpenShift is a plus.
Experience working in 5G Core networks or telecom industry solutions is advantageous.

Diversity and Inclusion Statement:

Tejas Networks is an equal opportunity employer. We celebrate diversity and are committed to creating all-inclusive environment for all employees.
We welcome applicants of all backgrounds regardless of race color, religion, gender, sexual orientation, age or veteran status.
Our goal is to build a workforce that reflects the diverse communities we serve and to ensure every employee feels valued and respected.

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4261398722,https://www.linkedin.com/jobs/view/4261398722,Pending,2025-07-24 19:00:04.052692,2025-07-25 18:00:04.168802,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.

Job Description

Your Career

Prisma® Cloud Compute Edition delivers a cloud workload protection platform (CWPP) for modern enterprises, providing holistic protection across hosts, containers, and serverless deployments in any cloud, throughout the software lifecycle. Prisma Cloud Compute Edition is cloud native and API-enabled, protecting all your workloads regardless of their underlying compute technology or the cloud in which they run. In addition, it provides Web Application and API Security (WAAS) for any cloud native architecture.

You will work firsthand with our valued customers to address their complex post-sales concerns where analysis of situations or data requires an in-depth evaluation of many factors. You’re a critical thinker in understanding the methods, techniques, and evaluation criteria for obtaining results. You’ll enjoy networking with key contacts outside your own area of expertise, with a strong capability of detailing difficult technical issues to both non-technical and technical professionals.

You will be a designated customer advocate who will assist in providing tailored support, weekly reviews, root cause analysis for critical issues, release review and upgrade planning, and a quarterly business review. You will provide personalised support and become deeply familiar with your implementation and business priorities to proactively drive best practices and help you continuously improve your security posture.

You will regularly participate in technical discussions with multi-functional teams, creating an environment of transparency that ultimately leads to better products, better working environments, and better cybersecurity. Your quick thinking and support to our clients provides the fast assistance they need to keep their environments secure – which means you’ll need to move quickly, thoughtfully, and provide technical assistance as needed (often, in high pressure situations).

Your Impact

Provide technical services around Prisma Cloud technologies, including troubleshooting and best practices observations for Cloud platforms (any one of the cloud platforms: AWS, or GCP or Azure)
Provide configurations, troubleshooting, and best practices to customers in Linux-based environments.
Manage support cases to ensure issues are recorded, tracked, resolved, and follow-ups are completed in a timely manner
Perform fault isolation and root cause analysis for technical issues.
Publish Technical Support Bulletins and other technical documentation in the Knowledge Base to assist customers and improve operational efficiency.
Review technical documentation for training materials, technical marketing collateral, manuals, troubleshooting guides, etc.
Collaborate with the Engineering team to influence the operability of Prisma Cloud and its integrations with other technologies.
Occasional travel to customer sites may be required in the event of a critical situation.
Participate in an infrequent weekend on-call rotation and provide after-hours support as needed.
Provide on-call support 24x7 only on an as-needed basis, with minimal frequency.

Qualifications

Your Experience 

BE/B.Tech engineering, equivalent technical degree or equivalent military experience required
Customer Support 8 plus years of experience in a Technical Assistance Center (TAC) or in direct support or consulting roles with customers
Expertise in Cloud Platforms : Proven hands on experience in anyone of the Cloud Platforms (AWS or Azure or GCP)
Linux and API troubleshooting - Linux operating systems and also knowledge about API based troubleshooting
Customer Interaction - Significant experience in a Technical Assistance Center (TAC) or in direct support or consulting roles with customers.
Problem Solving - Ability to troubleshoot independently in complex environments with mixed applications and protocols.
Communication - Excellent skills in collaborating with both technical and non-technical stakeholders.
Nice to Have / Preferred : 
Any Cloud certifications (AWS or GCP or Azure or CKA)
Knowledge of Network Security (or) Cloud Security (or) vulnerability management
Familiarity with container security tools (e.g., Twistlock)

Additional Information

The Team

Our Global Customer Support team is critical to our success and mission. As part of this team, you enable customer success by providing support to clients after they have purchased our products. Our dedication to our customers doesn’t stop once they sign – it evolves. As threats and technology change, we stay in step to accomplish our mission.

Our Commitment

We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-22 18:00:17.628894,2025-07-25 18:00:17.768624,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.

Job Description

Your Career

Palo Alto Networks is looking for a talented Site Reliability Engineer for our ever expanding Cloud Operations. The ideal candidate enjoys working in a fast-paced environment with highly innovative technologies. Our team partners closely with IT and Engineering groups and requires individuals to bring a can-do, positive attitude, with a focus on delivering exceptional customer support.

Your Impact

Implementing and supporting the Linux infrastructure as code where our globally distributed customer-facing platform runs. 
Provision, configure & support resilient hybrid cloud deployment architecture using the automation framework and make it more efficient
Manage Linux infrastructure CI/CD platform, work with other SREs in deploying and maintaining automation framework, capacity planning, create and review PKI operational runbooks.
Manage scalability, capacity planning, redundancy, and resiliency.
Maintain service availability and performance SLAs based on business and product requirements.
Contribute to documentation related to design, deployment, validation, operations and DR/BCP.
Design proactive service monitoring, alerting and trend analysis of underlying infrastructure, and support the operations team in implementation.
Build and operate compute fabric for 1000s of VMs, Kubernetes Clusters. Develop scripts, build tools and write code to automate routine tasks.
Provide technical support to platform users
Respond to security implementation and audits of the environment.
Plan maintenance windows, write up change requests, present technical updates.
Participate in On-Call support including participating in RCA as required.

Qualifications

Your Experience 

Strong hands-on Linux experience in managing and supporting Linux server infrastructure in CentOS/RHEL/Ubuntu.
Bachelors/Masters degree in Computer Science, Information Technology or technical stream with the equivalent combination of work experience required.
Design and performance tuning for Linux infrastructure and API, in-depth knowledge of multi-tier web applications.
Experience in developing and managing APIs, understanding of API infrastructure optimization and security.
In-depth knowledge of Certificate Lifecycle Management
Fluent in Linux security & system hardening, vulnerability management & patching process. Familiarity with CIS compliance levels.
Must be comfortable with Ansible, Chef or similar configuration management tool to manage infrastructure as code and source code control systems such as GIT or SVN.
Ability to work cross-functionally across multiple business units, such as product development and engineering
Must be able to collaborate with a global team spread across multiple time zones. 
Passion, drive, energy, a sense of humour and a great attitude!
6+ years of relevant experience, Bachelor or Master’s degree in Computer Science or a related technical field.
Experience with administration and orchestration of cloud computing (AWS, GCP, etc.) running virtual or container environments. 
Good user and admin Linux skills (Ubuntu a plus).Experience with virtual networking.
Working experience with IaC tools like Terraform and Ansible. Knowledge of Python and shell scripting.
Experience with CI/CD development using platforms like - Jenkins, Harness, Artifactory.
Solid problem solving, troubleshooting, critical thinking, communication, and teamwork skills.
Passion for automation and monitoring instrumentation in the code.

Additional Information

The Team

Working at a high-tech cybersecurity company within Information Technology is a once-in-a-lifetime opportunity. You’ll join the brightest minds in technology, creating, building, and supporting tools and enabling our global teams on the front line of defense against cyberattacks.

We’re connected by one mission but driven by the impact of that mission and what it means to protect our way of life in the digital age. Join a dynamic and fast-paced team of people who feel excited by the prospect of a challenge and feel a thrill at resolving technical gaps that inhibit productivity.

Our Commitment

We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.

Covid-19 Vaccination Information for Palo Alto Networks Jobs

Vaccine requirements and disclosure obligations vary by country.
Unless applicable law requires otherwise, you must be vaccinated for COVID or qualify for a reasonable accommodation if:
The job requires accessing a company worksite
The job requires in-person customer contact and the customer has implemented such requirements
You choose to access a Palo Alto Networks worksite
If you have questions about the vaccine requirements of this particular position based on your location or job requirements, please inquire with the recruiter.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-25 01:00:23.162103,2025-07-25 18:00:23.376876,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

4+ years as DevOps Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
Proficiency in the cloud (GCP - preferred)
Proficiency with Terraform and HashiCorp tools
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage (Optional) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.


Is role eligible for Immigration Sponsorship? No. Please note that we will not sponsor applicants for work visas for this position.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-23 18:01:00.396735,2025-07-25 18:01:00.488789,Required experience is high,"
About the job
We are actively looking for DevOps Engineer.
About the Role
The Software Engineer will be responsible for developing and maintaining software solutions that meet the needs of our clients.
Qualification:

Experience: 10 years to 18 years
Notice Period: Immediate Joiner
Location: Bangalore

Required Skills
AWS
Preferred Skills
Experience with additional programming languages
Familiarity with cloud services

Experience required 10 > Current Experience 6. Skipping this job!
",Skipped,Not Available
4271843195,https://www.linkedin.com/jobs/view/4271843195,Pending,2025-07-25 02:01:15.844931,2025-07-25 18:01:16.057543,Found a Bad Word in About Job,"
About the job
Our Mission

At Palo Alto Networks® everything starts and ends with our mission:

Being the cybersecurity partner of choice, protecting our digital way of life.

Our vision is a world where each day is safer and more secure than the one before. We are a company built on the foundation of challenging and disrupting the way things are done, and we’re looking for innovators who are as committed to shaping the future of cybersecurity as we are.

Who We Are

We take our mission of protecting the digital way of life seriously. We are relentless in protecting our customers and we believe that the unique ideas of every member of our team contributes to our collective success. Our values were crowdsourced by employees and are brought to life through each of us everyday - from disruptive innovation and collaboration, to execution. From showing up for each other with integrity to creating an environment where we all feel included.

As a member of our team, you will be shaping the future of cybersecurity. We work fast, value ongoing learning, and we respect each employee as a unique individual. Knowing we all have different needs, our development and personal wellbeing programs are designed to give you choice in how you are supported. This includes our FLEXBenefits wellbeing spending account with over 1,000 eligible items selected by employees, our mental and financial health resources, and our personalized learning opportunities - just to name a few!

At Palo Alto Networks, we believe in the power of collaboration and value in-person interactions. This is why our employees generally work full time from our office with flexibility offered where needed. This setup fosters casual conversations, problem-solving, and trusted relationships. Our goal is to create an environment where we all win with precision.



Job Description

Your Career

We are looking for a motivated Senior SRE Engineer to join the Cortex Devops Production group based at our India development ( IDC ) center. In this position you will be working side by side with Cortex Cyber Security Research group. You will be responsible for planning, executing, and reporting on various infrastructure and code projects for Cortex Cyber Security Research Group. Additionally, you will manage and execute high-pressure production maintenance tasks and address related issues.

More information about the Cortex product can be found here

Your Impact

You will take full end-to-end responsibility for the production environment of our SaaS product deployed on GCP
You will build tools for the automatic remediation of known issues
You will develop Infrastructure-as-code which will be used to orchestrate production and dev environments
You will design, build, maintain, and scale production services with thousands of Kubernetes clusters
You will secure the production environments and add in new security tools and features both internal Palo Alto Networks and other market-leading technologies
You will work closely with development teams to design and enhance software architecture to improve scalability, service reliability, cost, and performance
You will build CI pipelines and automation processes
Participate in the on-call rotation supporting the applications and infrastructure
You will research cutting-edge technologies and deploy them to production

Qualifications

Your Experience 

10+ years as DevOps/ SRE Engineer (or equal role) with a passion for technology and strong motivation and responsibility for high reliability and service level
Release automation in the past via GitLab . ArgoCD etc. 
Proficiency with code language (Python / Go - preferred)
High proficiency with Linux
GCP Proficiency in the cloud 
Proficiency with Terraform and HashiCorp tools
HPA via Karpenter / Keda with custom metrics. 
High proficiency with virtualized and containerized environments (Kubernetes and Docker)
Proficiency with CI/CD and Configuration Management (Jenkins preferred)
Proficiency with DB such as Cassandra, ScyllaDB, MemSQL, MySQL - An advantage( Optional ) 
Experience with working with internal and external customers and stakeholders
Managing a high-scale production environment
Excellent communication and interpersonal skills, ability to work and coordinate between multiple teams
Ability to grasp new technologies quickly and prioritize and multitask on multiple responsibilities

Additional Information

The Team

To stay ahead of the curve, it’s critical to know where the curve is, and how to anticipate the changes we’re facing. For the fastest-growing cybersecurity company, the curve is the evolution of cyberattacks and access technology and the products and services that dedicatedly address them. Our engineering team is at the core of our products – connected directly to the mission of preventing cyberattacks and enabling secure access to all on-prem and cloud applications. They are constantly innovating – challenging the way we, and the industry, think about Access and security. These engineers aren’t shy about building products to solve problems no one has pursued before. They define the industry, instead of waiting for directions. We need individuals who feel comfortable in ambiguity, excited by the prospect of challenge, and empowered by the unknown risks facing our everyday lives that are only enabled by a secure digital environment.

Our engineering team is provided with an unrivaled chance to create the products and practices that will support our company growth over the next decade, defining the cybersecurity industry as we know it. If you see the potential of how incredible people and products can transform a business, this is the team for you. If the prospect of affecting tens of millions of people, enabling them to work remotely securely and easily in ways never done before, thrill you - you belong with us.

Our Commitment



We’re problem solvers that take risks and challenge cybersecurity’s status quo. It’s simple: we can’t accomplish our mission without diverse teams innovating, together.

We are committed to providing reasonable accommodations for all qualified individuals with a disability. If you require assistance or accommodation due to a disability or special need, please contact <NAME_EMAIL>.

Palo Alto Networks is an equal opportunity employer. We celebrate diversity in our workplace, and all qualified applicants will receive consideration for employment without regard to age, ancestry, color, family or medical care leave, gender identity or expression, genetic information, marital status, medical condition, national origin, physical or mental disability, political affiliation, protected veteran status, race, religion, sex (including pregnancy), sexual orientation, or other legally protected characteristics.

All your information will be kept confidential according to EEO guidelines.

Contains bad word ""Legal"". Skipping this job!
",Skipped,Not Available
**********,https://www.linkedin.com/jobs/view/**********,Pending,2025-07-24 18:01:49.227814,2025-07-25 18:01:49.321903,Found a Bad Word in About Job,"
About the job
About the role:
We’re hiring a founding engineer to join us in shaping the next generation of consumer app infrastructure. This will be an in-person role in Bangalore. We’re flexible on compensation and are willing to pay top of band for the right person. We’re looking for someone who’s hungry to grow and be as AI-native as possible.

What are we looking for:
Design and implement scalable backend services using Java.
Participate in architecture and design discussions with a focus on scalability and performance.
Write clean, maintainable code with unit and integration tests.
Collaborate with cross-functional teams to deliver high-quality features.
Take ownership of modules and drive them end-to-end.

Why should you join us:
Experience the chaos of 0-1 startup journey along with other smart and hungry people
Build cutting edge tech to help scale the next generation of consumer apps
Court-side view to applied AI; You will have free reign to apply AI wherever you see fit, and fund any learning / SaaS expenses that you want to experiment with
Broaden your skill-set - totally open to structuring a path to whatever role you’re interested in over time (sales, ML, product, design, data etc.)

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4270966533,https://www.linkedin.com/jobs/view/4270966533,Pending,2025-07-23 18:01:56.366456,2025-07-25 18:01:56.472263,Found a Bad Word in About Job,"
About the job
Job Title : PowerShell Developer

Job Type :Contract
shift : EST Shifts

Qualifications

Strong Communication and Customer Service skills
Proficient Analytical Skills for problem-solving and decision making
Experience with Administrative Assistance tasks and procedures
Basic understanding of Finance operations and principles
Familiarity with scripting languages, predominantly PowerShell
Working knowledge of cloud services and infrastructure automation
Bachelor’s degree in Computer Science, Information Technology, or related field

Relevant industry certifications are a plus

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4272063512,https://www.linkedin.com/jobs/view/4272063512,Pending,2025-07-25 17:39:02.547739,2025-07-25 18:02:02.676417,Found a Bad Word in About Job,"
About the job
Company Description
HomeTriangle is a fast-growing tech-driven brand transforming the way Indians discover and book home services. We're expanding our creative team to amplify our visual storytelling across digital channels.

Role Description
We are seeking a passionate Graphic Designer with strong illustration skills (Adobe Illustrator proficiency required) who can bring creative marketing, branding, and campaign ideas to life. The ideal candidate should also be comfortable in front of the camera, as you may participate in short shoots for our social media, team stories, or campaign content.

Responsibilities
Design eye-catching graphics for digital, print, and social media campaigns.
Illustrate original assets and infographics using Adobe Illustrator (character design, icons, doodles, process visuals, etc.).
Collaborate with marketing and product teams to ideate engaging concepts and branding materials.
Participate in brainstorming visual campaigns and translating ideas into compelling visuals.
Help drive the visual direction for branding, digital ads, website, and OOH campaigns.
Ensure all assets align with HomeTriangle’s brand guidelines and tone.
Stay up-to-date with design trends and propose innovative approaches.

Qualifications
Proven work experience as a graphic designer or illustrator, with a strong portfolio.
Advanced proficiency in Adobe Illustrator.
Solid command of Photoshop/InDesign and general design systems.
A creative eye for color, composition, branding, and storytelling.
Excellent communication and collaboration skills.
Comfortable and confident in front of the camera; previous exposure in team shoots/short-form video is a plus.
Ability to handle constructive feedback and meet short deadlines.

Bonus Qualifications To Have
Experience in content production for social media (Reels, YouTube Shorts, etc.).
Familiarity with agency or in-house design environments.

What We Offer
Opportunity to be part of a home-grown, impact-driven brand.
Dynamic and creative working environment in the heart of Bangalore.
Creative freedom and space for personal brand-building.
supportive team, and plenty of opportunities for growth.

Portfolio Required: Attach/link recent graphic and illustration samples.

Contains bad word ""Marketing"". Skipping this job!
",Skipped,Not Available
4270156744,https://www.linkedin.com/jobs/view/4270156744,Pending,2025-07-25 16:02:11.574646,2025-07-25 18:02:11.710592,Found a Bad Word in About Job,"
About the job
Designation: B2B Academia
Experience: 1 - 4 Years
Education: Any Degree
Industry Type: Education / E-Learn Sales
Functional Area: B2B Academia / Sales
Category: Sales
Filter: Full time

Job Description

Connecting with Top Universities/Colleges in India and position Maven Silicon's Academia solutions
Driving quarterly revenue from the assigned Universities/Colleges
Positioning Maven Silicon Academia Services to College Management, HOD's and TPOs
PPT presentation to the college management to establish value propositions to Colleges/Universities
Researching and Prospecting the Colleges/Universities and create a right value proposition for each Institution.
Working with the College stakeholders and figuring out different engagement models to drive revenue
Attending Event offline and Online connected with the Institutions to ensure close contacts with the right stakeholders of the Institutions
Travelling to the Institution locations and meet with them and close deals

 Desired Candidate Profile

Excellent communication skills in English,
Speaking regional languages like Kannada/Telugu/Tamil/Hindi would be an added advantage.
Interest in the Education Sector is preferred.

For more details kindly contact 7406043555, <EMAIL>

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4255534651,https://www.linkedin.com/jobs/view/4255534651,Pending,2025-07-25 17:02:14.977520,2025-07-25 18:02:15.067469,Found a Bad Word in About Job,"
About the job
Company Description
 At SeeKNEO IT Solutions, we specialize in crafting tailored digital solutions that drive business growth. Based in Bangalore, we are a top mobile app and custom application development company committed to delivering high-quality services across multiple domains. Our expertise includes digital product development, custom application development, website design, UI/UX design, digital marketing, SEO, branding, and mobile app development. Partner with us to empower your business with cutting-edge technology and strategic digital solutions.
 Role Description
 This is a full-time on-site role for a Business Development Manager located in Bengaluru. The Business Development Manager will be responsible for identifying new business opportunities, developing strategic partnerships, and driving revenue growth. Day-to-day tasks include market research, preparing business proposals, negotiating contracts, and building relationships with potential clients. The role involves collaborating with cross-functional teams to develop and implement growth strategies.
 Qualifications
  Strong skills in Business Development, Sales, and Strategic Partnerships
Experience in Market Research and Analytics
Excellent Communication, Negotiation, and Presentation skills
Ability to build and maintain relationships with clients and partners
Proficiency in CRM software and other business development tools
Bachelor's degree in Business, Marketing, or a related field preferred
Experience in IT, Software Development, or related industry is a plus
Ability to work independently and as part of a team

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4272054240,https://www.linkedin.com/jobs/view/4272054240,Pending,2025-07-25 17:02:20.316991,2025-07-25 18:02:20.405894,Found a Bad Word in About Job,"
About the job
The ideal candidate will have experience in all stages of the sales cycle. They should be confident with building new client relationship and maintaining existing ones. They should have evidence of strong skills and possess good negotiation skills. 
 Responsibilities
Build relationships with prospective clients
Maintain consistent contact with existing clients
Manage sales pipeline
Analyze market and establish competitive advantages
Track metrics to ensure targets are hit

Qualifications

Bachelor's degree 1+ years in B2B
Experience in full sales cycle including deal closing Demonstrated sales success
Strong negotiation skills
Strong communication and presentation skills
CRM experience is preferred

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4272012916,https://www.linkedin.com/jobs/view/4272012916,Pending,2025-07-25 15:02:30.974739,2025-07-25 18:02:31.056269,Found a Bad Word in About Job,"
About the job
Company Description
 
We suggest you enter details here.

 Role Description
 
This is a full-time on-site role for a Trainer with a FinTech background. The position is located in Bengaluru. The Trainer will be responsible for developing and delivering training programs, creating training materials, conducting training sessions, evaluating trainee performance, and providing constructive feedback. The Trainer will also be responsible for staying updated on industry trends and best practices to ensure training programs remain relevant and effective.

 Qualifications
 
Experience in developing and delivering training programs
Strong understanding of FinTech, digital payments, and financial services
Excellent presentation and communication skills
Ability to create and manage engaging training materials
Strong organizational and time management skills
Ability to evaluate trainee performance and provide constructive feedback
Experience working in a FinTech company is a plus
Bachelor's degree in Finance, Business, Education, or related field

Contains bad word ""Finance"". Skipping this job!
",Skipped,Not Available
4272016216,https://www.linkedin.com/jobs/view/4272016216,Pending,2025-07-25 13:02:34.446591,2025-07-25 18:02:34.695322,Found a Bad Word in About Job,"
About the job
🚨 We’re Hiring! – Generalist Designer
📍 Location: Bangalore (On-site)
📅 Joining: Immediate (August 2025)
📌 Department: Marketing & Brand
🧰 Experience: 2+ years
Reach out to: <EMAIL>
About Zeliot
Zeliot is a technology company building Condense, a real-time data streaming platform that empowers enterprises to process, manage, and visualize data at scale. We enable smarter, connected ecosystems across industries like automotive, logistics, and manufacturing — bridging the gap between raw telemetry and actionable insights.
What You’ll Do
🎨 Branding
Design brand assets: decks, brochures, flyers, internal culture materials, and event collaterals
Templatize key documents to maintain visual consistency
Ensure cohesive brand identity across all touchpoints
📢 Marketing Design
Translate marketing campaigns into visually compelling creatives
Create digital assets for social media, blogs, case studies, and web
Communicate product features visually for end users
📍 Exhibitions & Booths
Design booth graphics and supporting materials for industry events
🖥️ UX/UI & Product Design Support
Join UX brainstorms and contribute to UI design consistency
Collaborate with product and dev teams for seamless visual implementation
🤝 Collaboration
Work closely with marketing, sales, and content teams
Participate in creative discussions and help drive brand direction
Bring initiative, creativity, and ownership to every task
What We’re Looking For
2+ years of experience in design (preferably in tech or B2B)
Proficient in Adobe Creative Suite, Figma, and presentation tools
Strong portfolio across both digital and print
Excellent communication skills and ownership mindset
Experience working cross-functionally with marketing, dev, and leadership teams
Exposure to SaaS/enterprise products is a plus
Bonus: Familiarity with branding, motion graphics, or tools like Framer
💼 If this sounds like you, drop us a line at 👉 <EMAIL>
Let’s build bold, meaningful designs together at Zeliot!
# Job Opportunities# Generalist Designer# hr@zeliot#

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
4270180403,https://www.linkedin.com/jobs/view/4270180403,Pending,2025-07-25 17:25:39.313841,2025-07-25 18:02:39.505751,Found a Bad Word in About Job,"
About the job
Job Summary
Rently is looking for an experienced and technically skilled Salesforce Administrator to lead the administration, optimization, and technical governance of our Salesforce ecosystem. You will support multiple clouds including Sales Cloud, Service Cloud, Revenue Cloud, and Marketing Cloud, enabling scalable automation, security enforcement, and high availability for critical business operations. This role is crucial in our transition from third-party management to a fully in-house Salesforce Center of Excellence.
Key Responsibilities
Serve as the primary technical admin for multi-cloud Salesforce architecture across business units.
Configure and manage users, permission sets, profiles, roles, public groups, and OWD/sharing settings.
Develop and manage declarative automation: Flows, Process Builder, Approval Processes, and Validation Rules.
Implement security best practices including 2FA, session timeout policies, IP restrictions, and field-level security.
Create and maintain data models, schema builder designs, and custom metadata types.
Administer Marketing Cloud Account Engagement (Pardot) assets, segmentation, forms, and engagement studio.
Oversee CPQ, Spiff, and Revenue Cloud configurations – quoting, pricing, product catalog setup.
Build advanced reports and dashboards for executive stakeholders using joined reports and bucket fields.
Perform scheduled sandbox refreshes, metadata backups, and change set deployments.
Collaborate with engineering for custom development integration (Apex, LWC, REST/SOAP APIs, Workbench).
Support production deployments, UAT cycles, and documentation for audits and governance reviews.
Required Skills & Experience
5+ years as Salesforce Administrator in a multi-cloud org (Sales, Service, Revenue, Community, or Marketing Cloud).
Salesforce Certified Administrator (Required). Advanced Admin or Platform App Builder is a plus.
Hands-on experience with Flow Orchestrator, Dynamic Forms, and Custom Metadata Types.
Strong understanding of data architecture, API integrations, and platform event handling.
Exposure to tools like Workbench, Data Loader, VS Code with Salesforce CLI.
Experience working with AppExchange packages like CPQ Plus, Spiff, and Backup & Restore.
Knowledge of Change Set deployments, unlocked packages, and DevOps tools (Gearset/Copado preferred).
Ability to manage high-volume environments (100+ users) and optimize login-based licenses (Community users).
Administer and maintain Rently’s custom eCommerce portal built on Salesforce Experience Cloud, ensuring smooth functionality, secure access, and a seamless user experience.
Nice to Have
Familiarity with OmniStudio, Einstein Analytics, and Data Cloud segmentation logic.
Understanding of Salesforce Shield, encryption, and audit trail configurations.
Ability to write SOQL, basic Apex triggers, and troubleshoot Lightning Web Components.
Previous experience transitioning Salesforce orgs between license editions or environments.
Why Join Rently?
Be part of a rapidly growing PropTech company transforming the rental industry.
Opportunity to lead Salesforce platform governance and roadmap at an enterprise scale.
Get exposure to a modern stack including Marketing Cloud, Revenue Cloud, Spiff, and CPQ.
Collaborate with a tech-first team that embraces automation, observability, and innovation.
Competitive compensation, flexible work environment, and strong ownership culture.

Contains bad word ""Sales"". Skipping this job!
",Skipped,Not Available
